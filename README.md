# 🚀 Next.js Starter Kit

A modern Next.js Starter Kit pre-configured with powerful tools and libraries for building full-stack applications quickly and efficiently.

## 🧩 Tech Stack

-⚛️ Next.js – React framework with file-based routing and SSR

-💨 Tailwind CSS – Utility-first CSS framework

-🧱 shadcn/ui – Accessible and stylish UI components built on Radix and Tailwind

-🦄 Lucide React – Icon library

-🧠 Zustand – Lightweight global state management

-🔀 App Router with file-based routing

-🔎 TanStack Query – Powerful async data management

-🧪 API Routes – Backend logic with Next.js (serverless functions)

---

## ⚙️ Getting Started
### 1. Clone the Repository
``` bash
git clone https://github.com/Lwant-02/Next-Js-Starter-Kit.git
cd Next-Js-Starter-Kit
```

---


## ⚙️ Getting Started
### 2. Install Dependencies
``` bash
npm install
# or
yarn install
```

---

## ⚙️ Getting Started
### 3. Run the Development Server
``` bash
npm run dev
# or
yarn dev
```


